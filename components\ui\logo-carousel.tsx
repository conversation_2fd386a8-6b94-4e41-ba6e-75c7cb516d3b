"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import Image from "next/image";

interface Logo {
  id: number;
  name: string;
  src: string;
}

interface LogoColumnProps {
  logos: Logo[];
  columnIndex: number;
  currentTime: number;
}

function LogoColumn({ logos, columnIndex, currentTime }: LogoColumnProps) {
  const CYCLE_DURATION = 3000; // Slower for smoother animation
  const columnDelay = columnIndex * 300; // More delay between columns
  const adjustedTime = (currentTime + columnDelay) % (CYCLE_DURATION * logos.length);
  const currentIndex = Math.floor(adjustedTime / CYCLE_DURATION);
  const currentLogo = logos[currentIndex];

  // Calculate smooth transition progress
  const progress = (adjustedTime % CYCLE_DURATION) / CYCLE_DURATION;
  const opacity = progress < 0.1 ? progress * 10 : progress > 0.9 ? (1 - progress) * 10 : 1;

  return (
    <div
      className="relative h-12 w-24 overflow-hidden md:h-20 md:w-40 opacity-0 animate-fade-in-up"
      style={{
        animationDelay: `${columnIndex * 0.15}s`,
        animationFillMode: 'forwards'
      }}
    >
      <div
        key={`${currentLogo.id}-${currentIndex}`}
        className="absolute inset-0 flex items-center justify-center transition-all duration-700 ease-in-out"
        style={{ opacity }}
      >
        <Image
          src={currentLogo.src}
          alt={currentLogo.name}
          width={140}
          height={60}
          className="h-auto w-auto max-h-[85%] max-w-[85%] object-contain transition-all duration-300 hover:scale-105"
        />
      </div>
    </div>
  );
}

interface LogoCarouselProps {
  columns?: number;
  logos: Logo[];
}

export function LogoCarousel({ columns = 5, logos }: LogoCarouselProps) {
  const [logoColumns, setLogoColumns] = useState<Logo[][]>([]);
  const [time, setTime] = useState(0);

  const distributeLogos = useCallback(
    (logos: Logo[]) => {
      const shuffled = [...logos].sort(() => Math.random() - 0.5);
      const result: Logo[][] = Array.from({ length: columns }, () => []);

      shuffled.forEach((logo, index) => {
        result[index % columns].push(logo);
      });

      const maxLength = Math.max(...result.map((col) => col.length));
      result.forEach((col) => {
        while (col.length < maxLength) {
          col.push(shuffled[Math.floor(Math.random() * shuffled.length)]);
        }
      });

      return result;
    },
    [columns]
  );

  useEffect(() => {
    setLogoColumns(distributeLogos(logos));
  }, [logos, distributeLogos]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTime((prev) => prev + 50); // Smoother animation with smaller increments
    }, 50);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex justify-center gap-2 md:gap-4 py-8 overflow-x-hidden">
      {logoColumns.map((columnLogos, index) => (
        <LogoColumn
          key={index}
          logos={columnLogos}
          columnIndex={index}
          currentTime={time}
        />
      ))}
    </div>
  );
}
