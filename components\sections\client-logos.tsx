"use client"

import { useEffect, useRef } from "react"
import { LogoCarousel } from "@/components/ui/logo-carousel"

const clients = [
  { id: 1, name: "Godrej Properties", src: "/images/developers/godrej.webp" },
  { id: 2, name: "Sobha Limited", src: "/images/developers/sobha.webp" },
  { id: 3, name: "Lodha Group", src: "/images/developers/lodha.webp" },
  { id: 4, name: "Aaryan Group", src: "/images/developers/aaryan.webp" },
  { id: 5, name: "Aaryan Properties", src: "/images/developers/aaryan-2.webp" },
  { id: 6, name: "Gala Group", src: "/images/developers/gald.webp" },
  { id: 7, name: "HRG Group", src: "/images/developers/hrg.webp" },
  { id: 8, name: "Safal Group", src: "/images/developers/safal.webp" },
  { id: 9, name: "Savaliya Group", src: "/images/developers/savaliya.webp" },
  { id: 10, name: "Shaligram Group", src: "/images/developers/shaligram.webp" },
  { id: 11, name: "Shipl Group", src: "/images/developers/shipl.webp" },
  { id: 12, name: "Swagat Group", src: "/images/developers/swagat.webp" },
  { id: 13, name: "Swati Group", src: "/images/developers/swati.webp" },
  { id: 14, name: "Arista Homes", src: "/images/developers/arista.webp" },
  { id: 15, name: "Constera Group", src: "/images/developers/constera.webp" },
  { id: 16, name: "Deep Group", src: "/images/developers/deep.webp" },
  { id: 17, name: "E Square", src: "/images/developers/e.webp" },
  { id: 18, name: "HR Group", src: "/images/developers/hr.webp" },
  { id: 19, name: "Pacific Group", src: "/images/developers/pacific.webp" },
  { id: 20, name: "PD Group", src: "/images/developers/pd.webp" },
  { id: 21, name: "Ratnanjali Group", src: "/images/developers/ratnanjali.webp" },
  { id: 22, name: "Venus Group", src: "/images/developers/venus.webp" },
  { id: 23, name: "Zaveri Group", src: "/images/developers/zaveri.webp" },
]

export default function ClientLogos() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-burgundy-50 via-white to-burgundy-50 relative overflow-hidden w-full max-w-full">
      {/* Minimalist Background Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large Circle - Top Left */}
        <div className="absolute -top-32 -left-32 w-64 h-64 bg-gradient-to-br from-burgundy-100/30 to-burgundy-200/20 rounded-full blur-3xl animate-pulse-subtle"></div>

        {/* Medium Circle - Bottom Right */}
        <div className="absolute -bottom-24 -right-24 w-48 h-48 bg-gradient-to-tl from-burgundy-200/25 to-burgundy-100/15 rounded-full blur-2xl animate-float-minimal"></div>

        {/* Geometric Shapes */}
        <div className="absolute top-1/4 right-1/4 w-16 h-16 bg-burgundy-100/20 rotate-45 animate-spin-slow"></div>
        <div className="absolute bottom-1/4 left-1/4 w-12 h-12 bg-burgundy-200/25 rotate-12 animate-bounce-minimal"></div>

        {/* Subtle Lines */}
        <div className="absolute top-1/3 left-0 w-32 h-px bg-gradient-to-r from-transparent via-burgundy-200/40 to-transparent animate-pulse-gentle"></div>
        <div className="absolute bottom-1/3 right-0 w-24 h-px bg-gradient-to-l from-transparent via-burgundy-300/30 to-transparent animate-pulse-gentle"></div>

        {/* Small Decorative Dots */}
        <div className="absolute top-16 right-16 w-2 h-2 bg-burgundy-300/50 rounded-full animate-bounce-minimal"></div>
        <div className="absolute bottom-16 left-16 w-1 h-1 bg-burgundy-400/40 rounded-full animate-pulse-gentle"></div>
        <div className="absolute top-1/2 left-8 w-1.5 h-1.5 bg-burgundy-200/60 rounded-full animate-float-minimal"></div>
        <div className="absolute top-3/4 right-8 w-2 h-2 bg-burgundy-100/50 rounded-full animate-bounce-minimal"></div>

        {/* Abstract Curved Shape */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 opacity-5">
          <svg viewBox="0 0 200 200" className="w-full h-full animate-spin-very-slow">
            <path
              d="M50,100 Q100,50 150,100 Q100,150 50,100"
              fill="none"
              stroke="currentColor"
              strokeWidth="1"
              className="text-burgundy-400"
            />
          </svg>
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10 w-full max-w-full overflow-hidden">
        <div className="text-center space-y-4 mb-12 animate-on-scroll opacity-0 translate-y-8">
          <p className="text-sm font-medium tracking-widest text-burgundy-600 uppercase">
            Our Valued Clients
          </p>
          <h2 className="text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-none text-gray-900">
            Trusted by{" "}
            <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
              Industry Leaders
            </span>
          </h2>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          <LogoCarousel logos={clients} columns={5} />
        </div>
      </div>
    </section>
  )
}
