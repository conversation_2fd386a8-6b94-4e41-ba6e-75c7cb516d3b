"use client"

import React, { useEffect, useState } from "react"
import <PERSON><PERSON> from "lottie-react"
// Direct import of animation data to prevent fallback flash
import preloaderAnimation from "@/public/animations/pre-loader.json"

interface PreloaderProps {
  animationData?: any // Lottie animation JSON data
  duration?: number // Duration in milliseconds
  onComplete?: () => void
}

const Preloader: React.FC<PreloaderProps> = ({
  animationData,
  duration = 2000, // Match the provider duration
  onComplete
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [progress, setProgress] = useState(0)

  // Use directly imported animation data as fallback to prevent flash
  const finalAnimationData = animationData || preloaderAnimation

  useEffect(() => {
    // Simplified progress animation
    let progressValue = 0
    const increment = 100 / (duration / 50) // 50ms intervals

    const progressInterval = setInterval(() => {
      progressValue += increment
      if (progressValue >= 100) {
        progressValue = 100
        clearInterval(progressInterval)
      }
      setProgress(Math.round(progressValue))
    }, 50)

    // Hide preloader after duration
    const timer = setTimeout(() => {
      setIsVisible(false)
      if (onComplete) {
        setTimeout(() => {
          onComplete()
        }, 500) // Wait for exit animation
      }
    }, duration)

    return () => {
      clearInterval(progressInterval)
      clearTimeout(timer)
    }
  }, [duration, onComplete])

  // Show preloader only when visible
  if (!isVisible) return null

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-white"
      style={{
        display: 'flex',
        visibility: 'visible',
        opacity: 1
      }}
    >
      <div className="flex flex-col items-center justify-center space-y-8">
        {/* Lottie Animation Only */}
        <div className="w-40 h-40 md:w-48 md:h-48 flex items-center justify-center">
          <Lottie
            animationData={finalAnimationData}
            loop={true}
            autoplay={true}
            style={{ width: "100%", height: "100%" }}
            rendererSettings={{
              preserveAspectRatio: 'xMidYMid slice'
            }}
          />
        </div>

        {/* Progress Bar */}
        <div className="relative w-[200px]">
          <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-burgundy-600 to-burgundy-500 rounded-full transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-center text-sm text-gray-600 mt-2">
            Loading... {progress}%
          </p>
        </div>

        {/* Loading Text */}
        <div className="text-center">
          <h2 className="text-xl font-playfair text-gray-800 mb-2">
            Welcome to Dwelling Desire
          </h2>
          <p className="text-sm text-gray-600">
            Your luxury real estate experience is loading...
          </p>
        </div>
      </div>
    </div>
  )
}

export default Preloader
